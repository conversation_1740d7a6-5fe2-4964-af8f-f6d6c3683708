package com.webank.maling.ai.documentation;

import com.webank.maling.ai.config.SpringAIConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 响应式AI文档生成服务
 * 使用Spring AI框架，支持响应式编程和流式处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReactiveAIDocumentationService {
    
    private final ChatModel chatModel;
    private final AIPromptBuilder promptBuilder;
    private final SpringAIConfig.AIServiceProperties properties;
    
    @Autowired
    public ReactiveAIDocumentationService(ChatModel chatModel, 
                                        AIPromptBuilder promptBuilder,
                                        SpringAIConfig.AIServiceProperties properties) {
        this.chatModel = chatModel;
        this.promptBuilder = promptBuilder;
        this.properties = properties;
        log.info("响应式AI文档生成服务已初始化");
    }
    
    /**
     * 响应式生成文档内容
     * 
     * @param context 文档生成上下文
     * @return 生成的文档内容的Mono
     */
    public Mono<String> generateDocumentationAsync(DocumentationGenerationContext context) {
        return Mono.fromCallable(() -> {
                    log.info("开始为层级 {} 生成文档，入口点: {}", context.getLevel(), context.getEntryPointId());
                    return promptBuilder.buildPromptForLevel(context);
                })
                .filter(prompt -> prompt != null && !prompt.trim().isEmpty())
                .switchIfEmpty(Mono.error(new IllegalArgumentException("构建提示词失败")))
                .flatMap(this::callAIServiceReactive)
                .map(content -> postProcessContent(content, context))
                .doOnSuccess(content -> log.info("成功生成层级 {} 的文档，内容长度: {} 字符", 
                        context.getLevel(), content != null ? content.length() : 0))
                .doOnError(error -> log.error("生成文档时发生错误", error));
    }
    
    /**
     * 流式生成文档内容
     * 
     * @param context 文档生成上下文
     * @return 生成内容的流
     */
    public Flux<String> generateDocumentationStream(DocumentationGenerationContext context) {
        return Mono.fromCallable(() -> promptBuilder.buildPromptForLevel(context))
                .filter(prompt -> prompt != null && !prompt.trim().isEmpty())
                .switchIfEmpty(Mono.error(new IllegalArgumentException("构建提示词失败")))
                .flatMapMany(this::callAIServiceStream)
                .doOnNext(chunk -> log.debug("接收到文档片段，长度: {}", chunk.length()))
                .doOnComplete(() -> log.info("文档流式生成完成，层级: {}", context.getLevel()))
                .doOnError(error -> log.error("流式生成文档时发生错误", error));
    }
    
    /**
     * 批量生成文档
     * 
     * @param contexts 文档生成上下文列表
     * @return 生成结果的Flux
     */
    public Flux<DocumentationResult> generateBatchDocumentation(Flux<DocumentationGenerationContext> contexts) {
        return contexts
                .flatMap(context -> 
                    generateDocumentationAsync(context)
                            .map(content -> DocumentationResult.success(context, content))
                            .onErrorReturn(error -> DocumentationResult.failure(context, error.getMessage()))
                )
                .doOnNext(result -> log.info("批量生成完成一个文档，成功: {}", result.isSuccess()))
                .doOnComplete(() -> log.info("批量文档生成全部完成"));
    }
    
    /**
     * 响应式调用AI服务
     */
    private Mono<String> callAIServiceReactive(String promptText) {
        return Mono.fromCallable(() -> {
                    Prompt prompt = new Prompt(promptText);
                    return chatModel.call(prompt);
                })
                .map(response -> response.getResult().getOutput().getContent())
                .retryWhen(Retry.backoff(properties.getMaxRetryAttempts(), 
                        Duration.ofMillis(properties.getRetryDelayMs()))
                        .doBeforeRetry(retrySignal -> 
                                log.warn("AI服务调用失败，进行第 {} 次重试", retrySignal.totalRetries() + 1)))
                .doOnSuccess(content -> log.debug("AI服务调用成功，内容长度: {}", 
                        content != null ? content.length() : 0))
                .timeout(Duration.ofMinutes(5)) // 5分钟超时
                .onErrorMap(throwable -> new RuntimeException("AI服务调用失败", throwable));
    }
    
    /**
     * 流式调用AI服务
     */
    private Flux<String> callAIServiceStream(String promptText) {
        return Flux.defer(() -> {
                    try {
                        Prompt prompt = new Prompt(promptText);
                        return chatModel.stream(prompt)
                                .map(response -> response.getResult().getOutput().getContent())
                                .filter(content -> content != null && !content.isEmpty());
                    } catch (Exception e) {
                        return Flux.error(new RuntimeException("流式AI服务调用失败", e));
                    }
                })
                .retryWhen(Retry.backoff(properties.getMaxRetryAttempts(), 
                        Duration.ofMillis(properties.getRetryDelayMs())))
                .timeout(Duration.ofMinutes(10)); // 流式调用允许更长的超时时间
    }
    
    /**
     * 后处理生成的内容
     */
    private String postProcessContent(String content, DocumentationGenerationContext context) {
        if (content == null) {
            return null;
        }
        
        // 1. 清理多余的空白字符
        String processed = content.trim().replaceAll("\\n{3,}", "\n\n");
        
        // 2. 添加生成信息
        String footer = String.format("\n\n---\n*本说明书由AI自动生成，层级: %d，生成时间: %s*",
                context.getLevel(), LocalDateTime.now().toString());
        
        return processed + footer;
    }
    
    /**
     * 检查AI服务是否可用
     */
    public Mono<Boolean> isServiceAvailable() {
        return Mono.fromCallable(() -> {
                    Prompt testPrompt = new Prompt("请回复'服务正常'");
                    var response = chatModel.call(testPrompt);
                    String content = response.getResult().getOutput().getContent();
                    return content != null && !content.trim().isEmpty();
                })
                .timeout(Duration.ofSeconds(30))
                .onErrorReturn(false)
                .doOnNext(available -> log.debug("AI服务可用性检查结果: {}", available));
    }
    
    /**
     * 获取服务状态信息
     */
    public Mono<AIServiceStatus> getServiceStatus() {
        return isServiceAvailable()
                .map(available -> AIServiceStatus.builder()
                        .available(available)
                        .model(properties.getChatModel())
                        .endpoint(properties.getChatUrl())
                        .lastCheckTime(LocalDateTime.now())
                        .build())
                .onErrorReturn(error -> AIServiceStatus.builder()
                        .available(false)
                        .error(error.getMessage())
                        .lastCheckTime(LocalDateTime.now())
                        .build());
    }
    
    /**
     * 使用模板生成文档
     */
    public Mono<String> generateWithTemplate(String templateName, Map<String, Object> variables) {
        return Mono.fromCallable(() -> {
                    PromptTemplate template = new PromptTemplate(getTemplate(templateName));
                    return template.create(variables);
                })
                .flatMap(prompt -> Mono.fromCallable(() -> chatModel.call(prompt)))
                .map(response -> response.getResult().getOutput().getContent())
                .retryWhen(Retry.backoff(properties.getMaxRetryAttempts(), 
                        Duration.ofMillis(properties.getRetryDelayMs())))
                .timeout(Duration.ofMinutes(5));
    }
    
    /**
     * 获取模板内容（可以从文件或数据库加载）
     */
    private String getTemplate(String templateName) {
        // 这里可以从文件系统或数据库加载模板
        return switch (templateName) {
            case "basic" -> "请为以下代码生成说明文档：{code}";
            case "detailed" -> "请为以下代码生成详细的技术文档，包括功能说明、参数解释和使用示例：{code}";
            case "api" -> "请为以下API生成接口文档：{api}";
            default -> "请生成文档：{content}";
        };
    }
    
    /**
     * 文档生成结果
     */
    public static class DocumentationResult {
        private final DocumentationGenerationContext context;
        private final String content;
        private final boolean success;
        private final String errorMessage;
        
        private DocumentationResult(DocumentationGenerationContext context, String content, 
                                  boolean success, String errorMessage) {
            this.context = context;
            this.content = content;
            this.success = success;
            this.errorMessage = errorMessage;
        }
        
        public static DocumentationResult success(DocumentationGenerationContext context, String content) {
            return new DocumentationResult(context, content, true, null);
        }
        
        public static DocumentationResult failure(DocumentationGenerationContext context, String errorMessage) {
            return new DocumentationResult(context, null, false, errorMessage);
        }
        
        // Getters
        public DocumentationGenerationContext getContext() { return context; }
        public String getContent() { return content; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
    }
}
