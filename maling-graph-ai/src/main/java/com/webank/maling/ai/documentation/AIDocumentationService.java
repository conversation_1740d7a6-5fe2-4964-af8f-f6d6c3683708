package com.webank.maling.ai.documentation;

import com.webank.maling.ai.service.AIServiceFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.concurrent.CompletableFuture;

/**
 * AI文档生成服务（重构版本）
 * 使用新的响应式AI服务，保持向后兼容性
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AIDocumentationService {

    private final AIServiceFacade aiServiceFacade;
    private final AIPromptBuilder promptBuilder;

    @Autowired
    public AIDocumentationService(AIServiceFacade aiServiceFacade, AIPromptBuilder promptBuilder) {
        this.aiServiceFacade = aiServiceFacade;
        this.promptBuilder = promptBuilder;
        log.info("AI文档生成服务已初始化（使用响应式服务）");
    }

    /**
     * 生成文档内容（异步版本，推荐使用）
     *
     * @param context 文档生成上下文
     * @return 生成的文档内容的Mono
     */
    public Mono<String> generateDocumentationAsync(DocumentationGenerationContext context) {
        log.info("开始异步生成文档，层级: {}, 入口点: {}", context.getLevel(), context.getEntryPointId());
        return aiServiceFacade.generateDocumentationAsync(context)
                .doOnSuccess(content -> log.info("异步文档生成完成，内容长度: {}", 
                        content != null ? content.length() : 0))
                .doOnError(error -> log.error("异步文档生成失败", error));
    }

    /**
     * 生成文档内容（同步版本，兼容性接口）
     *
     * @param context 文档生成上下文
     * @return 生成的文档内容
     */
    public String generateDocumentation(DocumentationGenerationContext context) {
        log.info("开始同步生成文档，层级: {}, 入口点: {}", context.getLevel(), context.getEntryPointId());
        try {
            String content = aiServiceFacade.generateDocumentationSync(context);
            log.info("同步文档生成完成，内容长度: {}", content != null ? content.length() : 0);
            return content;
        } catch (Exception e) {
            log.error("同步文档生成失败", e);
            throw new RuntimeException("文档生成失败", e);
        }
    }

    /**
     * 生成文档内容（CompletableFuture版本，兼容性接口）
     *
     * @param context 文档生成上下文
     * @return 生成的文档内容的CompletableFuture
     */
    public CompletableFuture<String> generateDocumentationFuture(DocumentationGenerationContext context) {
        log.info("开始Future文档生成，层级: {}, 入口点: {}", context.getLevel(), context.getEntryPointId());
        return aiServiceFacade.generateDocumentationFuture(context)
                .whenComplete((content, throwable) -> {
                    if (throwable != null) {
                        log.error("Future文档生成失败", throwable);
                    } else {
                        log.info("Future文档生成完成，内容长度: {}", 
                                content != null ? content.length() : 0);
                    }
                });
    }

    /**
     * 为指定层级生成文档（兼容性方法）
     *
     * @param entryPointId 入口点ID
     * @param level 层级
     * @return 生成的文档内容
     */
    public String generateDocumentationForLevel(String entryPointId, int level) {
        DocumentationGenerationContext context = DocumentationGenerationContext.builder()
                .entryPointId(entryPointId)
                .level(level)
                .build();
        
        return generateDocumentation(context);
    }

    /**
     * 异步为指定层级生成文档
     *
     * @param entryPointId 入口点ID
     * @param level 层级
     * @return 生成的文档内容的CompletableFuture
     */
    public CompletableFuture<String> generateDocumentationForLevelAsync(String entryPointId, int level) {
        DocumentationGenerationContext context = DocumentationGenerationContext.builder()
                .entryPointId(entryPointId)
                .level(level)
                .build();
        
        return generateDocumentationFuture(context);
    }

    /**
     * 检查AI服务是否可用
     *
     * @return 服务是否可用
     */
    public boolean isServiceAvailable() {
        try {
            return aiServiceFacade.checkServiceHealth()
                    .map(status -> status.isDocumentationServiceAvailable())
                    .block();
        } catch (Exception e) {
            log.error("检查AI服务可用性时发生错误", e);
            return false;
        }
    }

    /**
     * 获取服务健康状态
     *
     * @return 服务健康状态
     */
    public AIServiceFacade.AIServiceHealthStatus getServiceHealth() {
        try {
            return aiServiceFacade.checkServiceHealth().block();
        } catch (Exception e) {
            log.error("获取AI服务健康状态时发生错误", e);
            return null;
        }
    }

    /**
     * 预热服务
     */
    public void warmupService() {
        log.info("开始预热AI文档生成服务");
        try {
            aiServiceFacade.warmupServices().block();
            log.info("AI文档生成服务预热完成");
        } catch (Exception e) {
            log.warn("AI文档生成服务预热失败", e);
        }
    }

    // ==================== 静态工厂方法（兼容性支持） ====================

    /**
     * 获取实例（兼容性方法）
     * 注意：在Spring环境中应该使用依赖注入
     */
    @Deprecated
    public static AIDocumentationService getInstance() {
        log.warn("使用了已废弃的getInstance()方法，建议使用Spring依赖注入");
        // 这里可以通过ApplicationContext获取Bean实例
        throw new UnsupportedOperationException("请使用Spring依赖注入获取AIDocumentationService实例");
    }

    /**
     * 批量生成文档（简化版本）
     *
     * @param contexts 文档生成上下文列表
     * @return 生成结果列表的CompletableFuture
     */
    public CompletableFuture<java.util.List<String>> generateBatchDocumentation(
            java.util.List<DocumentationGenerationContext> contexts) {
        
        return aiServiceFacade.generateBatchDocumentation(contexts)
                .map(result -> result.isSuccess() ? result.getContent() : null)
                .filter(content -> content != null)
                .collectList()
                .toFuture();
    }

    /**
     * 使用模板生成文档
     *
     * @param templateName 模板名称
     * @param variables 变量映射
     * @return 生成的文档内容
     */
    public String generateWithTemplate(String templateName, java.util.Map<String, Object> variables) {
        try {
            return aiServiceFacade.generateWithTemplate(templateName, variables).block();
        } catch (Exception e) {
            log.error("使用模板生成文档失败", e);
            throw new RuntimeException("模板文档生成失败", e);
        }
    }

    /**
     * 异步使用模板生成文档
     *
     * @param templateName 模板名称
     * @param variables 变量映射
     * @return 生成的文档内容的CompletableFuture
     */
    public CompletableFuture<String> generateWithTemplateAsync(String templateName, 
                                                              java.util.Map<String, Object> variables) {
        return aiServiceFacade.generateWithTemplate(templateName, variables).toFuture();
    }
}
