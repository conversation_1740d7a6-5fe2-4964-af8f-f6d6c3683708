package com.webank.maling.ai.service;

import com.webank.maling.ai.documentation.DocumentationGenerationContext;
import com.webank.maling.ai.documentation.ReactiveAIDocumentationService;
import com.webank.maling.ai.vector.ReactiveVectorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * AI服务门面类
 * 提供统一的AI服务接口，包括文档生成和向量生成
 * 同时提供同步和异步接口以保持向后兼容性
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class AIServiceFacade {
    
    private final ReactiveAIDocumentationService documentationService;
    private final ReactiveVectorService vectorService;
    
    @Autowired
    public AIServiceFacade(ReactiveAIDocumentationService documentationService,
                          ReactiveVectorService vectorService) {
        this.documentationService = documentationService;
        this.vectorService = vectorService;
        log.info("AI服务门面已初始化");
    }
    
    // ==================== 文档生成服务 ====================
    
    /**
     * 异步生成文档（推荐使用）
     */
    public Mono<String> generateDocumentationAsync(DocumentationGenerationContext context) {
        return documentationService.generateDocumentationAsync(context);
    }
    
    /**
     * 同步生成文档（兼容性接口）
     */
    public String generateDocumentationSync(DocumentationGenerationContext context) {
        return documentationService.generateDocumentationAsync(context)
                .timeout(Duration.ofMinutes(10))
                .block();
    }
    
    /**
     * 转换为CompletableFuture（兼容性接口）
     */
    public CompletableFuture<String> generateDocumentationFuture(DocumentationGenerationContext context) {
        return documentationService.generateDocumentationAsync(context)
                .timeout(Duration.ofMinutes(10))
                .toFuture();
    }
    
    /**
     * 流式生成文档
     */
    public Flux<String> generateDocumentationStream(DocumentationGenerationContext context) {
        return documentationService.generateDocumentationStream(context);
    }
    
    /**
     * 批量生成文档
     */
    public Flux<ReactiveAIDocumentationService.DocumentationResult> generateBatchDocumentation(
            List<DocumentationGenerationContext> contexts) {
        return documentationService.generateBatchDocumentation(Flux.fromIterable(contexts));
    }
    
    /**
     * 使用模板生成文档
     */
    public Mono<String> generateWithTemplate(String templateName, Map<String, Object> variables) {
        return documentationService.generateWithTemplate(templateName, variables);
    }
    
    // ==================== 向量生成服务 ====================
    
    /**
     * 异步生成向量（推荐使用）
     */
    public Mono<float[]> generateVectorAsync(String text) {
        return vectorService.generateVectorAsync(text);
    }
    
    /**
     * 同步生成向量（兼容性接口）
     */
    public float[] generateVectorSync(String text) {
        return vectorService.generateVectorAsync(text)
                .timeout(Duration.ofMinutes(2))
                .block();
    }
    
    /**
     * 转换为CompletableFuture（兼容性接口）
     */
    public CompletableFuture<float[]> generateVectorFuture(String text) {
        return vectorService.generateVectorAsync(text)
                .timeout(Duration.ofMinutes(2))
                .toFuture();
    }
    
    /**
     * 批量生成向量
     */
    public Mono<List<float[]>> generateBatchVectors(List<String> texts) {
        return vectorService.generateBatchVectors(texts);
    }
    
    /**
     * 流式生成向量
     */
    public Flux<ReactiveVectorService.VectorResult> generateVectorStream(List<String> texts) {
        return vectorService.generateVectorStream(Flux.fromIterable(texts));
    }
    
    /**
     * 计算文本相似度
     */
    public Mono<Double> calculateSimilarity(String text1, String text2) {
        return vectorService.calculateSimilarity(text1, text2);
    }
    
    // ==================== 服务状态检查 ====================
    
    /**
     * 检查所有AI服务是否可用
     */
    public Mono<AIServiceHealthStatus> checkServiceHealth() {
        Mono<Boolean> docServiceAvailable = documentationService.isServiceAvailable();
        Mono<Boolean> vectorServiceAvailable = vectorService.isServiceAvailable();
        
        return Mono.zip(docServiceAvailable, vectorServiceAvailable)
                .map(tuple -> AIServiceHealthStatus.builder()
                        .documentationServiceAvailable(tuple.getT1())
                        .vectorServiceAvailable(tuple.getT2())
                        .overallHealthy(tuple.getT1() && tuple.getT2())
                        .checkTime(java.time.LocalDateTime.now())
                        .build())
                .doOnNext(status -> log.info("AI服务健康检查完成: {}", status));
    }
    
    /**
     * 获取文档服务状态
     */
    public Mono<com.webank.maling.ai.documentation.AIServiceStatus> getDocumentationServiceStatus() {
        return documentationService.getServiceStatus();
    }
    
    // ==================== 性能优化方法 ====================
    
    /**
     * 预热服务（在应用启动时调用）
     */
    public Mono<Void> warmupServices() {
        log.info("开始预热AI服务");
        
        Mono<String> warmupDoc = generateDocumentationAsync(
                DocumentationGenerationContext.builder()
                        .entryPointId("warmup")
                        .level(1)
                        .build())
                .onErrorReturn("warmup");
        
        Mono<float[]> warmupVector = generateVectorAsync("warmup text")
                .onErrorReturn(new float[0]);
        
        return Mono.zip(warmupDoc, warmupVector)
                .then()
                .doOnSuccess(v -> log.info("AI服务预热完成"))
                .doOnError(error -> log.warn("AI服务预热失败", error));
    }
    
    /**
     * 批量处理优化
     */
    public Flux<ProcessingResult> processInBatches(List<ProcessingTask> tasks, int batchSize) {
        return Flux.fromIterable(tasks)
                .buffer(batchSize)
                .flatMap(batch -> processBatch(batch))
                .doOnNext(result -> log.debug("批处理完成一个任务，成功: {}", result.isSuccess()))
                .doOnComplete(() -> log.info("所有批处理任务完成"));
    }
    
    /**
     * 处理单个批次
     */
    private Flux<ProcessingResult> processBatch(List<ProcessingTask> batch) {
        return Flux.fromIterable(batch)
                .flatMap(task -> {
                    if (task.getType() == ProcessingTask.TaskType.DOCUMENTATION) {
                        return generateDocumentationAsync(task.getDocContext())
                                .map(content -> ProcessingResult.success(task, content))
                                .onErrorReturn(error -> ProcessingResult.failure(task, error.getMessage()));
                    } else if (task.getType() == ProcessingTask.TaskType.VECTOR) {
                        return generateVectorAsync(task.getText())
                                .map(vector -> ProcessingResult.success(task, vector))
                                .onErrorReturn(error -> ProcessingResult.failure(task, error.getMessage()));
                    } else {
                        return Mono.just(ProcessingResult.failure(task, "未知任务类型"));
                    }
                });
    }
    
    // ==================== 内部类 ====================
    
    /**
     * AI服务健康状态
     */
    public static class AIServiceHealthStatus {
        private boolean documentationServiceAvailable;
        private boolean vectorServiceAvailable;
        private boolean overallHealthy;
        private java.time.LocalDateTime checkTime;
        
        public static AIServiceHealthStatusBuilder builder() {
            return new AIServiceHealthStatusBuilder();
        }
        
        public static class AIServiceHealthStatusBuilder {
            private boolean documentationServiceAvailable;
            private boolean vectorServiceAvailable;
            private boolean overallHealthy;
            private java.time.LocalDateTime checkTime;
            
            public AIServiceHealthStatusBuilder documentationServiceAvailable(boolean available) {
                this.documentationServiceAvailable = available;
                return this;
            }
            
            public AIServiceHealthStatusBuilder vectorServiceAvailable(boolean available) {
                this.vectorServiceAvailable = available;
                return this;
            }
            
            public AIServiceHealthStatusBuilder overallHealthy(boolean healthy) {
                this.overallHealthy = healthy;
                return this;
            }
            
            public AIServiceHealthStatusBuilder checkTime(java.time.LocalDateTime checkTime) {
                this.checkTime = checkTime;
                return this;
            }
            
            public AIServiceHealthStatus build() {
                AIServiceHealthStatus status = new AIServiceHealthStatus();
                status.documentationServiceAvailable = this.documentationServiceAvailable;
                status.vectorServiceAvailable = this.vectorServiceAvailable;
                status.overallHealthy = this.overallHealthy;
                status.checkTime = this.checkTime;
                return status;
            }
        }
        
        // Getters
        public boolean isDocumentationServiceAvailable() { return documentationServiceAvailable; }
        public boolean isVectorServiceAvailable() { return vectorServiceAvailable; }
        public boolean isOverallHealthy() { return overallHealthy; }
        public java.time.LocalDateTime getCheckTime() { return checkTime; }
        
        @Override
        public String toString() {
            return String.format("AIServiceHealthStatus{doc=%s, vector=%s, healthy=%s, time=%s}",
                    documentationServiceAvailable, vectorServiceAvailable, overallHealthy, checkTime);
        }
    }
    
    /**
     * 处理任务
     */
    public static class ProcessingTask {
        public enum TaskType { DOCUMENTATION, VECTOR }
        
        private final TaskType type;
        private final DocumentationGenerationContext docContext;
        private final String text;
        
        private ProcessingTask(TaskType type, DocumentationGenerationContext docContext, String text) {
            this.type = type;
            this.docContext = docContext;
            this.text = text;
        }
        
        public static ProcessingTask documentation(DocumentationGenerationContext context) {
            return new ProcessingTask(TaskType.DOCUMENTATION, context, null);
        }
        
        public static ProcessingTask vector(String text) {
            return new ProcessingTask(TaskType.VECTOR, null, text);
        }
        
        // Getters
        public TaskType getType() { return type; }
        public DocumentationGenerationContext getDocContext() { return docContext; }
        public String getText() { return text; }
    }
    
    /**
     * 处理结果
     */
    public static class ProcessingResult {
        private final ProcessingTask task;
        private final Object result;
        private final boolean success;
        private final String errorMessage;
        
        private ProcessingResult(ProcessingTask task, Object result, boolean success, String errorMessage) {
            this.task = task;
            this.result = result;
            this.success = success;
            this.errorMessage = errorMessage;
        }
        
        public static ProcessingResult success(ProcessingTask task, Object result) {
            return new ProcessingResult(task, result, true, null);
        }
        
        public static ProcessingResult failure(ProcessingTask task, String errorMessage) {
            return new ProcessingResult(task, null, false, errorMessage);
        }
        
        // Getters
        public ProcessingTask getTask() { return task; }
        public Object getResult() { return result; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
    }
}
