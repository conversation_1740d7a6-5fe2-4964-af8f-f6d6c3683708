package com.webank.maling.ai.documentation;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * AI服务状态信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class AIServiceStatus {
    
    /**
     * 服务是否可用
     */
    private boolean available;
    
    /**
     * 使用的模型名称
     */
    private String model;
    
    /**
     * 服务端点URL
     */
    private String endpoint;
    
    /**
     * 最后检查时间
     */
    private LocalDateTime lastCheckTime;
    
    /**
     * 错误信息（如果有）
     */
    private String error;
    
    /**
     * 响应时间（毫秒）
     */
    private Long responseTimeMs;
    
    /**
     * 服务版本
     */
    private String version;
    
    /**
     * 额外的状态信息
     */
    private java.util.Map<String, Object> additionalInfo;
    
    /**
     * 检查服务是否健康
     */
    public boolean isHealthy() {
        return available && error == null;
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (available) {
            return "服务正常";
        } else if (error != null) {
            return "服务异常: " + error;
        } else {
            return "服务不可用";
        }
    }
    
    /**
     * 添加额外信息
     */
    public void addInfo(String key, Object value) {
        if (additionalInfo == null) {
            additionalInfo = new java.util.HashMap<>();
        }
        additionalInfo.put(key, value);
    }
    
    /**
     * 获取额外信息
     */
    public Object getInfo(String key) {
        return additionalInfo != null ? additionalInfo.get(key) : null;
    }
    
    @Override
    public String toString() {
        return String.format("AIServiceStatus{available=%s, model='%s', endpoint='%s', lastCheck=%s}",
                available, model, endpoint, lastCheckTime);
    }
}
