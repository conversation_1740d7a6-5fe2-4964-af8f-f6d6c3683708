package com.webank.maling.ai.config;

import com.webank.maling.base.config.AppConfig;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.ChatClient;
import org.springframework.ai.embedding.EmbeddingClient;
import org.springframework.ai.openai.OpenAiChatClient;
import org.springframework.ai.openai.OpenAiEmbeddingClient;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * Spring AI 配置类
 * 配置OpenAI聊天模型和嵌入模型
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class SpringAIConfig {
    
    private final AppConfig appConfig;
    
    public SpringAIConfig() {
        this.appConfig = AppConfig.getInstance();
    }
    
    /**
     * 配置WebClient用于响应式HTTP调用
     */
    @Bean
    @Primary
    public WebClient webClient() {
        return WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
                .build();
    }
    
    /**
     * 配置OpenAI API客户端用于聊天
     */
    @Bean
    public OpenAiApi openAiChatApi() {
        return new OpenAiApi(appConfig.getChatUrl(), appConfig.getChatApiKey(), webClient());
    }
    
    /**
     * 配置OpenAI聊天模型
     */
    @Bean
    @Primary
    public OpenAiChatModel openAiChatModel() {
        var options = OpenAiChatModel.OpenAiChatOptions.builder()
                .withModel(appConfig.getChatModel())
                .withMaxTokens(4000)
                .withTemperature(0.7f)
                .build();
        
        return new OpenAiChatModel(openAiChatApi(), options);
    }
    
    /**
     * 配置OpenAI嵌入API客户端
     */
    @Bean
    public OpenAiEmbeddingApi openAiEmbeddingApi() {
        return new OpenAiEmbeddingApi(appConfig.getEmbeddingUrl(), appConfig.getEmbeddingApiKey(), webClient());
    }
    
    /**
     * 配置OpenAI嵌入模型
     */
    @Bean
    @Primary
    public EmbeddingModel embeddingModel() {
        var options = OpenAiEmbeddingModel.OpenAiEmbeddingOptions.builder()
                .withModel(appConfig.getEmbeddingModel())
                .build();
        
        return new OpenAiEmbeddingModel(openAiEmbeddingApi(), options);
    }
    
    /**
     * 配置AI服务属性
     */
    @Bean
    public AIServiceProperties aiServiceProperties() {
        return AIServiceProperties.builder()
                .chatModel(appConfig.getChatModel())
                .embeddingModel(appConfig.getEmbeddingModel())
                .chatUrl(appConfig.getChatUrl())
                .embeddingUrl(appConfig.getEmbeddingUrl())
                .maxTokens(4000)
                .temperature(0.7f)
                .maxRetryAttempts(3)
                .retryDelayMs(1000L)
                .build();
    }
    
    /**
     * AI服务配置属性
     */
    @Getter
    @Builder
    public static class AIServiceProperties {
        // Getters
        private String chatModel;
        private String embeddingModel;
        private String chatUrl;
        private String embeddingUrl;
        private Integer maxTokens;
        private Float temperature;
        private Integer maxRetryAttempts;
        private Long retryDelayMs;
    }
}
