package com.webank.maling.ai.vector;

import com.webank.maling.ai.config.SpringAIConfig;
import com.webank.maling.base.config.AppConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.EmbeddingRequest;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.List;
import java.util.stream.IntStream;

/**
 * 响应式向量生成服务
 * 使用Spring AI框架，支持响应式编程和批量处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReactiveVectorService {
    
    private final EmbeddingModel embeddingModel;
    private final SpringAIConfig.AIServiceProperties properties;
    private final AppConfig appConfig;
    
    @Autowired
    public ReactiveVectorService(EmbeddingModel embeddingModel, 
                               SpringAIConfig.AIServiceProperties properties) {
        this.embeddingModel = embeddingModel;
        this.properties = properties;
        this.appConfig = AppConfig.getInstance();
        log.info("响应式向量生成服务已初始化");
    }
    
    /**
     * 响应式生成单个文本的向量
     * 
     * @param text 文本内容
     * @return 向量的Mono
     */
    public Mono<float[]> generateVectorAsync(String text) {
        if (text == null || text.trim().isEmpty()) {
            log.warn("空文本提供给向量生成");
            return Mono.just(new float[appConfig.getMilvusDimension()]);
        }
        
        return Mono.fromCallable(() -> {
                    String processedText = preprocessText(text);
                    EmbeddingRequest request = new EmbeddingRequest(List.of(processedText), null);
                    return embeddingModel.call(request);
                })
                .map(this::extractFirstEmbedding)
                .retryWhen(Retry.backoff(properties.getMaxRetryAttempts(), 
                        Duration.ofMillis(properties.getRetryDelayMs()))
                        .doBeforeRetry(retrySignal -> 
                                log.warn("向量生成失败，进行第 {} 次重试", retrySignal.totalRetries() + 1)))
                .timeout(Duration.ofMinutes(2))
                .doOnSuccess(vector -> log.debug("成功生成向量，维度: {}", vector.length))
                .onErrorReturn(throwable -> {
                    log.error("向量生成失败", throwable);
                    return new float[appConfig.getMilvusDimension()];
                });
    }
    
    /**
     * 批量生成向量
     * 
     * @param texts 文本列表
     * @return 向量列表的Mono
     */
    public Mono<List<float[]>> generateBatchVectors(List<String> texts) {
        if (texts == null || texts.isEmpty()) {
            return Mono.just(List.of());
        }
        
        return Mono.fromCallable(() -> {
                    List<String> processedTexts = texts.stream()
                            .map(this::preprocessText)
                            .toList();
                    
                    EmbeddingRequest request = new EmbeddingRequest(processedTexts, null);
                    return embeddingModel.call(request);
                })
                .map(this::extractAllEmbeddings)
                .retryWhen(Retry.backoff(properties.getMaxRetryAttempts(), 
                        Duration.ofMillis(properties.getRetryDelayMs())))
                .timeout(Duration.ofMinutes(5))
                .doOnSuccess(vectors -> log.info("成功批量生成 {} 个向量", vectors.size()))
                .onErrorReturn(throwable -> {
                    log.error("批量向量生成失败", throwable);
                    return IntStream.range(0, texts.size())
                            .mapToObj(i -> new float[appConfig.getMilvusDimension()])
                            .toList();
                });
    }
    
    /**
     * 流式生成向量
     * 
     * @param textStream 文本流
     * @return 向量流
     */
    public Flux<VectorResult> generateVectorStream(Flux<String> textStream) {
        return textStream
                .filter(text -> text != null && !text.trim().isEmpty())
                .flatMap(text -> 
                    generateVectorAsync(text)
                            .map(vector -> VectorResult.success(text, vector))
                            .onErrorReturn(error -> VectorResult.failure(text, error.getMessage()))
                )
                .doOnNext(result -> log.debug("流式生成向量完成，成功: {}", result.isSuccess()))
                .doOnComplete(() -> log.info("向量流式生成全部完成"));
    }
    
    /**
     * 使用Document对象生成向量
     * 
     * @param document Spring AI Document对象
     * @return 向量的Mono
     */
    public Mono<float[]> generateVectorFromDocument(Document document) {
        return generateVectorAsync(document.getContent())
                .doOnSuccess(vector -> log.debug("为文档生成向量，ID: {}, 维度: {}", 
                        document.getId(), vector.length));
    }
    
    /**
     * 批量处理Document对象
     * 
     * @param documents Document列表
     * @return DocumentVector结果列表的Mono
     */
    public Mono<List<DocumentVector>> generateDocumentVectors(List<Document> documents) {
        if (documents == null || documents.isEmpty()) {
            return Mono.just(List.of());
        }
        
        List<String> texts = documents.stream()
                .map(Document::getContent)
                .toList();
        
        return generateBatchVectors(texts)
                .map(vectors -> {
                    return IntStream.range(0, documents.size())
                            .mapToObj(i -> new DocumentVector(documents.get(i), vectors.get(i)))
                            .toList();
                })
                .doOnSuccess(results -> log.info("成功为 {} 个文档生成向量", results.size()));
    }
    
    /**
     * 计算文本相似度
     * 
     * @param text1 文本1
     * @param text2 文本2
     * @return 相似度分数的Mono
     */
    public Mono<Double> calculateSimilarity(String text1, String text2) {
        Mono<float[]> vector1 = generateVectorAsync(text1);
        Mono<float[]> vector2 = generateVectorAsync(text2);
        
        return Mono.zip(vector1, vector2)
                .map(tuple -> cosineSimilarity(tuple.getT1(), tuple.getT2()))
                .doOnSuccess(similarity -> log.debug("计算文本相似度: {}", similarity));
    }
    
    /**
     * 检查向量服务是否可用
     */
    public Mono<Boolean> isServiceAvailable() {
        return generateVectorAsync("测试文本")
                .map(vector -> vector.length > 0)
                .timeout(Duration.ofSeconds(30))
                .onErrorReturn(false)
                .doOnNext(available -> log.debug("向量服务可用性检查结果: {}", available));
    }
    
    /**
     * 预处理文本
     */
    private String preprocessText(String text) {
        if (text == null) {
            return "";
        }
        
        return text.replace("\n", " ")
                .replace("\r", " ")
                .replace("\t", " ")
                .replaceAll("\\s+", " ")
                .trim();
    }
    
    /**
     * 提取第一个嵌入向量
     */
    private float[] extractFirstEmbedding(EmbeddingResponse response) {
        if (response.getResults().isEmpty()) {
            log.warn("嵌入响应为空");
            return new float[appConfig.getMilvusDimension()];
        }
        
        List<Double> embedding = response.getResults().get(0).getOutput();
        return embedding.stream()
                .map(Double::floatValue)
                .toArray(float[]::new);
    }
    
    /**
     * 提取所有嵌入向量
     */
    private List<float[]> extractAllEmbeddings(EmbeddingResponse response) {
        return response.getResults().stream()
                .map(result -> result.getOutput().stream()
                        .map(Double::floatValue)
                        .toArray(float[]::new))
                .toList();
    }
    
    /**
     * 计算余弦相似度
     */
    private double cosineSimilarity(float[] vectorA, float[] vectorB) {
        if (vectorA.length != vectorB.length) {
            throw new IllegalArgumentException("向量维度不匹配");
        }
        
        double dotProduct = 0.0;
        double normA = 0.0;
        double normB = 0.0;
        
        for (int i = 0; i < vectorA.length; i++) {
            dotProduct += vectorA[i] * vectorB[i];
            normA += Math.pow(vectorA[i], 2);
            normB += Math.pow(vectorB[i], 2);
        }
        
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
    
    /**
     * 向量生成结果
     */
    public static class VectorResult {
        private final String text;
        private final float[] vector;
        private final boolean success;
        private final String errorMessage;
        
        private VectorResult(String text, float[] vector, boolean success, String errorMessage) {
            this.text = text;
            this.vector = vector;
            this.success = success;
            this.errorMessage = errorMessage;
        }
        
        public static VectorResult success(String text, float[] vector) {
            return new VectorResult(text, vector, true, null);
        }
        
        public static VectorResult failure(String text, String errorMessage) {
            return new VectorResult(text, null, false, errorMessage);
        }
        
        // Getters
        public String getText() { return text; }
        public float[] getVector() { return vector; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
    }
    
    /**
     * 文档向量结果
     */
    public static class DocumentVector {
        private final Document document;
        private final float[] vector;
        
        public DocumentVector(Document document, float[] vector) {
            this.document = document;
            this.vector = vector;
        }
        
        // Getters
        public Document getDocument() { return document; }
        public float[] getVector() { return vector; }
    }
}
