# Spring AI 配置
spring:
  ai:
    openai:
      # API密钥配置
      api-key: ${OPENAI_API_KEY:your-api-key-here}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      
      # 聊天模型配置
      chat:
        enabled: true
        options:
          model: ${OPENAI_CHAT_MODEL:gpt-3.5-turbo}
          max-tokens: 4000
          temperature: 0.7
          top-p: 1.0
          frequency-penalty: 0.0
          presence-penalty: 0.0
          
      # 嵌入模型配置
      embedding:
        enabled: true
        options:
          model: ${OPENAI_EMBEDDING_MODEL:text-embedding-ada-002}
          
  # WebFlux配置
  webflux:
    # 编解码器配置
    codecs:
      max-in-memory-size: 10MB
      
# AI服务配置
ai:
  service:
    # 重试配置
    retry:
      max-attempts: 3
      delay-ms: 1000
      backoff-multiplier: 2.0
      max-delay-ms: 10000
      
    # 超时配置
    timeout:
      chat-timeout-minutes: 5
      embedding-timeout-minutes: 2
      stream-timeout-minutes: 10
      
    # 批处理配置
    batch:
      default-size: 10
      max-size: 100
      
    # 缓存配置
    cache:
      enabled: true
      ttl-minutes: 60
      max-size: 1000
      
    # 监控配置
    monitoring:
      enabled: true
      metrics-enabled: true
      health-check-interval-seconds: 60
      
# 响应式配置
reactor:
  # 调试模式
  debug-agent:
    enabled: false
    
# 日志配置
logging:
  level:
    com.webank.maling.ai: DEBUG
    org.springframework.ai: INFO
    reactor.netty: INFO
    
# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
