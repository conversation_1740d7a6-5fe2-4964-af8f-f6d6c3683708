# Spring AI 框架迁移指南

## 迁移概述

本次重构将原有的基于HttpClient的同步AI调用改为基于Spring AI框架的响应式调用，解决了IO阻塞问题并大大简化了代码。

## 主要改进

### 1. 解决IO阻塞问题
**原有问题**:
```java
// 同步阻塞调用，会阻塞线程
String response = httpClient.execute(request).toString();
```

**新的解决方案**:
```java
// 响应式非阻塞调用
Mono<String> response = chatModel.call(prompt)
    .map(result -> result.getResult().getOutput().getContent());
```

### 2. 代码简化

**重构前** - 手动构建HTTP请求（100+行代码）:
```java
public String generateDocumentation(String prompt) {
    try {
        // 构建JSON请求体
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("model", config.getChatModel());
        JsonArray messages = new JsonArray();
        JsonObject message = new JsonObject();
        message.addProperty("role", "user");
        message.addProperty("content", prompt);
        messages.add(message);
        requestBody.add("messages", messages);
        
        // 创建HTTP请求
        HttpPost request = new HttpPost(config.getChatUrl());
        request.setHeader("Authorization", "Bearer " + config.getChatApiKey());
        request.setHeader("Content-Type", "application/json");
        request.setEntity(new StringEntity(requestBody.toString()));
        
        // 执行请求并解析响应
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String responseBody = EntityUtils.toString(response.getEntity());
            JsonObject jsonResponse = Json.parseObject(responseBody);
            // ... 复杂的JSON解析逻辑
        }
    } catch (IOException e) {
        // 异常处理
    }
}
```

**重构后** - 使用Spring AI（5行代码）:
```java
public Mono<String> generateDocumentation(String prompt) {
    return chatModel.call(new Prompt(prompt))
            .map(response -> response.getResult().getOutput().getContent());
}
```

### 3. 响应式编程支持

**流式处理**:
```java
// 流式生成文档
public Flux<String> generateDocumentationStream(DocumentationGenerationContext context) {
    return chatModel.stream(new Prompt(promptText))
            .map(response -> response.getResult().getOutput().getContent());
}

// 批量处理
public Flux<DocumentationResult> generateBatchDocumentation(List<DocumentationGenerationContext> contexts) {
    return Flux.fromIterable(contexts)
            .flatMap(this::generateDocumentationAsync)
            .map(content -> DocumentationResult.success(context, content));
}
```

## 新的架构设计

### 1. 分层架构
```
Controller Layer
      ↓
AIServiceFacade (门面层)
      ↓
ReactiveAIDocumentationService / ReactiveVectorService (业务层)
      ↓
Spring AI Framework (框架层)
      ↓
OpenAI API (外部服务)
```

### 2. 核心组件

#### SpringAIConfig
- 配置OpenAI聊天模型和嵌入模型
- 配置WebClient用于响应式HTTP调用
- 统一的配置管理

#### ReactiveAIDocumentationService
- 响应式文档生成服务
- 支持异步、流式、批量处理
- 自动重试和超时处理

#### ReactiveVectorService
- 响应式向量生成服务
- 批量向量生成优化
- 相似度计算功能

#### AIServiceFacade
- 统一的AI服务门面
- 提供同步和异步接口
- 服务健康检查

## 使用示例

### 1. 文档生成

**异步生成（推荐）**:
```java
@Autowired
private AIServiceFacade aiServiceFacade;

// 异步生成
Mono<String> docMono = aiServiceFacade.generateDocumentationAsync(context);
docMono.subscribe(content -> {
    log.info("文档生成完成: {}", content);
});

// 或者使用block()获取结果（注意：会阻塞）
String content = docMono.block();
```

**流式生成**:
```java
Flux<String> docStream = aiServiceFacade.generateDocumentationStream(context);
docStream.subscribe(
    chunk -> log.info("接收到文档片段: {}", chunk),
    error -> log.error("生成失败", error),
    () -> log.info("文档生成完成")
);
```

**批量生成**:
```java
List<DocumentationGenerationContext> contexts = Arrays.asList(context1, context2, context3);
Flux<DocumentationResult> results = aiServiceFacade.generateBatchDocumentation(contexts);

results.subscribe(result -> {
    if (result.isSuccess()) {
        log.info("文档生成成功: {}", result.getContent());
    } else {
        log.error("文档生成失败: {}", result.getErrorMessage());
    }
});
```

### 2. 向量生成

**异步生成**:
```java
Mono<float[]> vectorMono = aiServiceFacade.generateVectorAsync("文本内容");
vectorMono.subscribe(vector -> {
    log.info("向量生成完成，维度: {}", vector.length);
});
```

**批量生成**:
```java
List<String> texts = Arrays.asList("文本1", "文本2", "文本3");
Mono<List<float[]>> vectorsMono = aiServiceFacade.generateBatchVectors(texts);
vectorsMono.subscribe(vectors -> {
    log.info("批量向量生成完成，数量: {}", vectors.size());
});
```

**相似度计算**:
```java
Mono<Double> similarityMono = aiServiceFacade.calculateSimilarity("文本1", "文本2");
similarityMono.subscribe(similarity -> {
    log.info("相似度: {}", similarity);
});
```

### 3. 服务健康检查

```java
Mono<AIServiceHealthStatus> healthMono = aiServiceFacade.checkServiceHealth();
healthMono.subscribe(status -> {
    log.info("AI服务状态: {}", status);
    if (status.isOverallHealthy()) {
        log.info("所有AI服务正常");
    } else {
        log.warn("部分AI服务异常");
    }
});
```

## 向后兼容性

为了保持向后兼容性，我们保留了原有的同步接口：

### AIDocumentationService
```java
// 新的异步接口（推荐）
public Mono<String> generateDocumentationAsync(DocumentationGenerationContext context);

// 兼容的同步接口
public String generateDocumentation(DocumentationGenerationContext context);

// 兼容的Future接口
public CompletableFuture<String> generateDocumentationFuture(DocumentationGenerationContext context);
```

### OpenAIVectorGenerator
```java
// 新的异步接口（推荐）
public Mono<float[]> generateVectorAsync(String text);

// 兼容的同步接口
public float[] generateVector(String text);

// 兼容的Future接口
public CompletableFuture<float[]> generateVectorFuture(String text);
```

## 迁移步骤

### 1. 更新依赖
```gradle
// 添加Spring AI依赖
implementation 'org.springframework.ai:spring-ai-openai-spring-boot-starter:1.0.0-M3'
implementation 'org.springframework.boot:spring-boot-starter-webflux:3.2.0'
```

### 2. 更新配置
```properties
# Spring AI配置会自动读取现有的OpenAI配置
spring.ai.openai.api-key=${OPENAI_API_KEY}
spring.ai.openai.chat.options.model=gpt-3.5-turbo
spring.ai.openai.embedding.options.model=text-embedding-ada-002
```

### 3. 更新代码

**原有代码**:
```java
AIDocumentationService service = AIDocumentationService.getInstance();
String content = service.generateDocumentationForLevel(entryPointId, level);
```

**新代码（推荐）**:
```java
@Autowired
private AIServiceFacade aiServiceFacade;

DocumentationGenerationContext context = DocumentationGenerationContext.simple(entryPointId, level);
Mono<String> contentMono = aiServiceFacade.generateDocumentationAsync(context);
```

**或者保持兼容性**:
```java
@Autowired
private AIDocumentationService documentationService;

DocumentationGenerationContext context = DocumentationGenerationContext.simple(entryPointId, level);
String content = documentationService.generateDocumentation(context);
```

## 性能优化

### 1. 批量处理
```java
// 批量处理可以显著提高性能
List<ProcessingTask> tasks = createTasks();
Flux<ProcessingResult> results = aiServiceFacade.processInBatches(tasks, 10);
```

### 2. 预热服务
```java
// 应用启动时预热AI服务
@PostConstruct
public void warmup() {
    aiServiceFacade.warmupServices().subscribe();
}
```

### 3. 错误处理和重试
```java
Mono<String> result = aiServiceFacade.generateDocumentationAsync(context)
    .retryWhen(Retry.backoff(3, Duration.ofSeconds(1)))
    .timeout(Duration.ofMinutes(5))
    .onErrorReturn("生成失败，使用默认内容");
```

## 监控和日志

### 1. 服务监控
```java
// 定期检查服务健康状态
@Scheduled(fixedRate = 60000)
public void checkHealth() {
    aiServiceFacade.checkServiceHealth()
        .subscribe(status -> {
            if (!status.isOverallHealthy()) {
                alertService.sendAlert("AI服务异常: " + status);
            }
        });
}
```

### 2. 性能监控
```java
// 使用Micrometer监控性能
Timer.Sample sample = Timer.start(meterRegistry);
aiServiceFacade.generateDocumentationAsync(context)
    .doFinally(signalType -> sample.stop(Timer.builder("ai.documentation.generation").register(meterRegistry)))
    .subscribe();
```

## 注意事项

1. **响应式编程**: 需要理解Mono和Flux的使用方式
2. **背压处理**: 在处理大量数据时注意背压控制
3. **错误处理**: 响应式流中的错误处理与传统方式不同
4. **测试**: 需要使用StepVerifier等工具测试响应式代码
5. **线程模型**: 响应式编程使用不同的线程模型，避免阻塞操作

## 总结

通过使用Spring AI框架，我们实现了：

1. **解决IO阻塞**: 使用响应式编程模型
2. **代码简化**: 减少90%的样板代码
3. **性能提升**: 支持批量处理和流式处理
4. **更好的错误处理**: 统一的重试和超时机制
5. **向后兼容**: 保留原有接口，平滑迁移

这次重构大大提高了系统的性能和可维护性，为后续的功能扩展奠定了良好的基础。
