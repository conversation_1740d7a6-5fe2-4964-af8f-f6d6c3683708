# 文档生成异步任务重构说明

## 重构概述

本次重构将文档生成流程从原有的内存队列 + Spring 异步任务模式，改为基于数据库任务表 + XXL-Job 定时任务的模式，实现更可靠的异步任务处理。

## 重构内容

### 1. 数据库表结构更新

#### 1.1 documentation_task 表新增字段
- `retry_count`: 重试次数，默认为 0
- `max_retry_count`: 最大重试次数，默认为 3
- `next_execute_time`: 下次执行时间，用于延迟重试
- `status`: 新增 `WAITING` 状态，表示等待执行

#### 1.2 任务状态流转
```
WAITING -> PENDING -> RUNNING -> COMPLETED/FAILED
                              -> WAITING (重试)
```

### 2. 核心组件

#### 2.1 XXL-Job 集成
- **XxlJobConfig**: XXL-Job 配置类
- **DocumentationJobHandler**: 任务处理器，包含三个定时任务：
  - `documentationGenerationJob`: 文档生成任务（每分钟执行）
  - `documentationCleanupJob`: 任务清理作业（每天凌晨2点）
  - `documentationRetryJob`: 失败任务重试作业（每小时执行）

#### 2.2 任务执行服务
- **DocumentationJobService**: 负责具体的任务执行逻辑
- **DocumentationTaskService**: 增强的任务管理服务，支持重试和状态管理

#### 2.3 重构的服务
- **ProgressiveDocumentationService**: 新增同步执行方法
- **DocumentationGenerator**: 新增任务创建方法
- **DocumentationController**: 更新为任务创建模式

### 3. 重试机制

#### 3.1 指数退避策略
- 第1次重试：1分钟后
- 第2次重试：2分钟后
- 第3次重试：4分钟后
- 最大延迟：60分钟

#### 3.2 重试条件
- 任务状态为 `FAILED`
- 重试次数 < 最大重试次数
- 达到下次执行时间

### 4. 配置参数

```properties
# XXL-Job 配置
xxl.job.admin.addresses=http://localhost:8080/xxl-job-admin
xxl.job.executor.appname=maling-documentation
xxl.job.executor.port=9999

# 任务处理配置
documentation.task.max_concurrent=10
documentation.task.batch_size=5
documentation.task.retry.max_count=3
documentation.task.cleanup.retention_days=30
```

## 使用方式

### 1. 部署 XXL-Job 管理平台

```bash
# 下载 XXL-Job
wget https://github.com/xuxueli/xxl-job/releases/download/2.4.1/xxl-job-admin-2.4.1.jar

# 启动管理平台
java -jar xxl-job-admin-2.4.1.jar
```

### 2. 配置定时任务

在 XXL-Job 管理平台中配置以下任务：

#### 2.1 文档生成任务
- **JobHandler**: `documentationGenerationJob`
- **Cron**: `0 * * * * ?` (每分钟执行)
- **参数**: `5` (批处理大小)

#### 2.2 任务清理作业
- **JobHandler**: `documentationCleanupJob`
- **Cron**: `0 0 2 * * ?` (每天凌晨2点)
- **参数**: `30` (保留天数)

#### 2.3 失败任务重试
- **JobHandler**: `documentationRetryJob`
- **Cron**: `0 0 * * * ?` (每小时执行)
- **参数**: `10` (批处理大小)

### 3. 数据库迁移

执行迁移脚本：
```sql
-- 执行 migration/V1.1__add_task_retry_fields.sql
```

### 4. 应用启动

```bash
# 启动应用
java -jar maling-graph-documentation.jar
```

## API 变化

### 1. 任务创建接口

**原有方式**（立即异步执行）：
```http
POST /api/documentation/generate/entry-point
```

**新方式**（创建任务，等待定时处理）：
```http
POST /api/documentation/generate/entry-point
```

响应保持不变，返回任务ID，但任务会进入 `WAITING` 状态等待定时任务处理。

### 2. 任务状态查询

```http
GET /api/documentation/task/{taskId}
```

新增状态：
- `WAITING`: 等待执行
- `PENDING`: 待处理（保留兼容性）
- `RUNNING`: 运行中
- `COMPLETED`: 已完成
- `FAILED`: 失败
- `CANCELLED`: 已取消

## 优势

### 1. 可靠性提升
- 任务持久化到数据库，避免内存丢失
- 支持应用重启后任务恢复
- 完善的重试机制

### 2. 可扩展性
- 支持多实例部署
- 任务负载均衡
- 灵活的调度策略

### 3. 可观测性
- 完整的任务执行日志
- 任务状态跟踪
- 性能监控

### 4. 运维友好
- 可视化任务管理
- 手动触发和停止
- 灵活的参数配置

## 注意事项

### 1. 兼容性
- 保留原有 API 接口
- 任务状态向后兼容
- 配置参数可选

### 2. 性能考虑
- 批处理减少数据库压力
- 合理的执行频率
- 索引优化查询性能

### 3. 监控告警
- 监控任务执行成功率
- 关注重试次数过多的任务
- 定期清理过期数据

## 故障排查

### 1. 任务不执行
- 检查 XXL-Job 连接状态
- 确认定时任务配置
- 查看应用日志

### 2. 任务重试过多
- 检查任务执行逻辑
- 分析错误日志
- 调整重试参数

### 3. 性能问题
- 监控数据库连接
- 检查任务执行时间
- 优化批处理大小
