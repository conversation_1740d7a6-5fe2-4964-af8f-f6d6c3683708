# 抽象基类重构说明

## 重构概述

为了减少代码重复，提高代码复用性和可维护性，我们将通用的任务处理逻辑抽离成抽象基类。现在文档生成任务相关的类都继承自这些抽象基类，复用通用逻辑。

## 抽象基类设计

### 1. AbstractTaskJobHandler<T>

**职责**: 封装 XXL-Job 任务处理器的通用逻辑

**核心功能**:
- 参数解析（批处理大小、整数参数等）
- 批处理任务执行
- 异常处理和日志记录
- 任务执行结果统计

**使用示例**:
```java
@Component
public class DocumentationJobHandler extends AbstractTaskJobHandler<DocumentationTask> {
    
    @XxlJob("documentationGenerationJob")
    public void documentationGenerationJob() {
        executeBatchJob(
            "文档生成任务",
            5, // 默认批处理大小
            taskService::getWaitingTasks,
            jobService::executeDocumentationTask
        );
    }
    
    @Override
    protected String getTaskId(DocumentationTask task) {
        return String.valueOf(task.getId());
    }
}
```

### 2. AbstractTaskService<T, S, M>

**职责**: 封装任务管理服务的通用逻辑

**核心功能**:
- 任务状态更新
- 重试机制处理
- 缓存管理
- 失败处理和指数退避

**使用示例**:
```java
@Service
public class DocumentationTaskService extends AbstractTaskService<DocumentationTask, DocumentationTask.TaskStatus, DocumentationTaskMapper> {
    
    // 只需要实现抽象方法，通用逻辑已在基类中实现
    @Override
    protected DocumentationTaskMapper getMapper() {
        return taskMapper;
    }
    
    @Override
    protected Long getTaskId(DocumentationTask task) {
        return task.getId();
    }
    
    // ... 其他抽象方法实现
}
```

### 3. AbstractJobService<T, S, D, R>

**职责**: 封装任务执行服务的通用逻辑

**核心功能**:
- 任务执行模板方法
- 状态管理
- 异常处理
- 批量执行

**使用示例**:
```java
@Service
public class DocumentationJobService extends AbstractJobService<DocumentationTask, DocumentationTask.TaskStatus, DocumentationGenerationDto, Documentation> {
    
    @Override
    protected Documentation doExecuteTask(DocumentationTask task, DocumentationGenerationDto dto) throws Exception {
        // 具体的任务执行逻辑
        return progressiveService.executeProgressiveGeneration(task, dto, entryPoint);
    }
    
    // ... 其他抽象方法实现
}
```

## 重构前后对比

### 重构前
```java
// DocumentationJobHandler - 大量重复的参数解析和异常处理代码
@XxlJob("documentationGenerationJob")
public void documentationGenerationJob() {
    String param = XxlJobHelper.getJobParam();
    try {
        int batchSize = 5;
        if (param != null && !param.trim().isEmpty()) {
            try {
                batchSize = Integer.parseInt(param.trim());
            } catch (NumberFormatException e) {
                log.warn("参数格式错误，使用默认值: {}", batchSize);
            }
        }
        
        List<DocumentationTask> tasks = taskService.getWaitingTasks(batchSize);
        int successCount = 0;
        int failCount = 0;
        
        for (DocumentationTask task : tasks) {
            try {
                boolean success = jobService.executeDocumentationTask(task);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                failCount++;
                log.error("处理任务异常", e);
            }
        }
        
        log.info("批处理完成 - 成功: {}, 失败: {}", successCount, failCount);
    } catch (Exception e) {
        log.error("执行任务异常", e);
        XxlJobHelper.handleFail("执行失败: " + e.getMessage());
    }
}
```

### 重构后
```java
// DocumentationJobHandler - 简洁的业务逻辑
@XxlJob("documentationGenerationJob")
public void documentationGenerationJob() {
    executeBatchJob(
        "文档生成任务",
        5,
        taskService::getWaitingTasks,
        jobService::executeDocumentationTask
    );
}
```

## 抽象基类的优势

### 1. 代码复用
- 通用逻辑只需要实现一次
- 减少重复代码，提高开发效率
- 统一的异常处理和日志记录

### 2. 一致性
- 所有任务处理器使用相同的模式
- 统一的参数解析和错误处理
- 一致的日志格式和异常处理

### 3. 可维护性
- 修改通用逻辑只需要修改基类
- 新增任务类型只需要继承基类并实现抽象方法
- 更容易进行单元测试

### 4. 扩展性
- 可以轻松添加新的任务类型
- 支持不同的任务状态和数据类型
- 灵活的模板方法模式

## 新增任务类型的步骤

如果要添加新的任务类型（比如数据清理任务），只需要：

### 1. 创建任务实体
```java
public class DataCleanupTask {
    private Long id;
    private TaskStatus status;
    // ... 其他字段
}
```

### 2. 创建任务服务
```java
@Service
public class DataCleanupTaskService extends AbstractTaskService<DataCleanupTask, TaskStatus, DataCleanupTaskMapper> {
    // 实现抽象方法
}
```

### 3. 创建任务执行服务
```java
@Service
public class DataCleanupJobService extends AbstractJobService<DataCleanupTask, TaskStatus, CleanupRequest, CleanupResult> {
    // 实现抽象方法
}
```

### 4. 创建任务处理器
```java
@Component
public class DataCleanupJobHandler extends AbstractTaskJobHandler<DataCleanupTask> {
    
    @XxlJob("dataCleanupJob")
    public void dataCleanupJob() {
        executeBatchJob(
            "数据清理任务",
            10,
            taskService::getWaitingTasks,
            jobService::executeTask
        );
    }
}
```

## 注意事项

### 1. 泛型类型
- 确保泛型参数正确匹配
- 注意任务实体、状态枚举、请求DTO和结果类型的对应关系

### 2. 抽象方法实现
- 必须实现所有抽象方法
- 注意方法的返回值和异常处理

### 3. 缓存管理
- 基类提供了缓存管理功能
- 子类可以根据需要重写缓存相关方法

### 4. 异常处理
- 基类提供了统一的异常处理
- 子类可以重写异常处理方法以实现特定逻辑

## 总结

通过抽象基类的重构，我们实现了：

1. **代码量减少**: 删除了大量重复代码
2. **一致性提升**: 统一的处理模式和异常处理
3. **可维护性增强**: 修改通用逻辑只需要修改基类
4. **扩展性提高**: 新增任务类型变得简单快捷

这种设计模式特别适合有多种类似任务处理需求的系统，可以大大提高开发效率和代码质量。
