-- 为 documentation_task 表添加重试相关字段
-- 执行时间：2024-07-30

-- 添加重试次数字段
ALTER TABLE documentation_task 
ADD COLUMN retry_count INT DEFAULT 0 COMMENT '重试次数' AFTER error_message;

-- 添加最大重试次数字段
ALTER TABLE documentation_task 
ADD COLUMN max_retry_count INT DEFAULT 3 COMMENT '最大重试次数' AFTER retry_count;

-- 添加下次执行时间字段
ALTER TABLE documentation_task 
ADD COLUMN next_execute_time TIMESTAMP NULL COMMENT '下次执行时间' AFTER max_retry_count;

-- 修改状态枚举，添加 WAITING 状态
ALTER TABLE documentation_task 
MODIFY COLUMN status ENUM('WAITING', 'PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED') 
NOT NULL DEFAULT 'WAITING' COMMENT '任务状态';

-- 添加索引以优化查询性能
ALTER TABLE documentation_task 
ADD INDEX idx_next_execute_time (next_execute_time);

ALTER TABLE documentation_task 
ADD INDEX idx_status_retry (status, retry_count, next_execute_time);

-- 更新现有任务的状态
-- 将所有 PENDING 状态的任务改为 WAITING 状态
UPDATE documentation_task 
SET status = 'WAITING', 
    retry_count = 0, 
    max_retry_count = 3,
    next_execute_time = NOW()
WHERE status = 'PENDING';

-- 为所有现有任务设置默认重试配置
UPDATE documentation_task 
SET retry_count = COALESCE(retry_count, 0),
    max_retry_count = COALESCE(max_retry_count, 3)
WHERE retry_count IS NULL OR max_retry_count IS NULL;
