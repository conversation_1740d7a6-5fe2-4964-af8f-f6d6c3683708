package com.webank.maling.documentation.service;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 抽象任务执行服务基类
 * 封装通用的任务执行逻辑，包括状态管理、异常处理等
 * 
 * @param <T> 任务实体类型
 * @param <S> 任务状态枚举类型
 * @param <D> 任务请求DTO类型
 * @param <R> 任务执行结果类型
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractJobService<T, S extends Enum<S>, D, R> {
    
    /**
     * 获取任务服务实例（子类实现）
     */
    protected abstract AbstractTaskService<T, S, ?> getTaskService();
    
    /**
     * 获取任务ID（子类实现）
     */
    protected abstract Long getTaskId(T task);
    
    /**
     * 获取任务描述信息（子类实现）
     */
    protected abstract String getTaskDescription(T task);
    
    /**
     * 检查任务是否可以执行（子类实现）
     */
    protected abstract boolean canExecuteTask(T task);
    
    /**
     * 执行具体的任务逻辑（子类实现）
     */
    protected abstract R doExecuteTask(T task, D dto) throws Exception;
    
    /**
     * 构建任务执行请求（子类实现）
     */
    protected abstract D buildTaskRequest(T task);
    
    /**
     * 获取运行中状态（子类实现）
     */
    protected abstract S getRunningStatus();
    
    /**
     * 获取完成状态（子类实现）
     */
    protected abstract S getCompletedStatus();
    
    /**
     * 获取失败状态（子类实现）
     */
    protected abstract S getFailedStatus();
    
    /**
     * 执行任务的通用模板方法
     * 
     * @param task 要执行的任务
     * @return 是否执行成功
     */
    public boolean executeTask(T task) {
        if (task == null) {
            log.error("任务为空，无法执行");
            return false;
        }
        
        Long taskId = getTaskId(task);
        String taskDescription = getTaskDescription(task);
        
        try {
            log.info("开始执行任务: {}, 描述: {}", taskId, taskDescription);
            
            // 检查任务是否可以执行
            if (!canExecuteTask(task)) {
                log.warn("任务 {} 不满足执行条件", taskId);
                return false;
            }
            
            // 更新任务状态为运行中
            getTaskService().updateStatus(taskId, getRunningStatus());
            
            // 构建任务请求
            D dto = buildTaskRequest(task);
            
            // 执行具体任务逻辑
            R result = doExecuteTask(task, dto);
            
            // 检查执行结果
            if (isSuccessResult(result)) {
                // 任务执行成功
                getTaskService().updateStatus(taskId, getCompletedStatus());
                log.info("任务 {} 执行成功", taskId);
                
                // 执行成功后的处理
                onTaskSuccess(task, result);
                
                return true;
            } else {
                // 任务执行失败
                String errorMsg = getErrorMessage(result);
                log.error("任务 {} 执行失败: {}", taskId, errorMsg);
                getTaskService().updateStatus(taskId, getFailedStatus(), errorMsg);
                
                // 执行失败后的处理
                onTaskFailure(task, errorMsg);
                
                return false;
            }
            
        } catch (Exception e) {
            log.error("执行任务 {} 时发生异常", taskId, e);
            
            try {
                // 更新任务失败状态
                String errorMsg = "执行异常: " + e.getMessage();
                getTaskService().updateStatus(taskId, getFailedStatus(), errorMsg);
                
                // 执行异常后的处理
                onTaskException(task, e);
                
            } catch (Exception ex) {
                log.error("更新任务失败状态时发生异常", ex);
            }
            
            return false;
        }
    }
    
    /**
     * 批量执行任务
     * 
     * @param tasks 任务列表
     * @return 执行结果统计
     */
    public BatchExecutionResult executeBatch(Iterable<T> tasks) {
        int successCount = 0;
        int failCount = 0;
        
        for (T task : tasks) {
            try {
                boolean success = executeTask(task);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                failCount++;
                log.error("批量执行任务时发生异常，任务ID: {}", getTaskId(task), e);
            }
        }
        
        return new BatchExecutionResult(successCount, failCount);
    }
    
    /**
     * 安全执行操作
     * 
     * @param operation 操作描述
     * @param action 要执行的操作
     */
    protected void safeExecute(String operation, Runnable action) {
        try {
            action.run();
        } catch (Exception e) {
            log.error("执行{}时发生异常", operation, e);
        }
    }
    
    /**
     * 判断执行结果是否成功（子类可重写）
     * 
     * @param result 执行结果
     * @return 是否成功
     */
    protected boolean isSuccessResult(R result) {
        return result != null;
    }
    
    /**
     * 获取错误信息（子类可重写）
     * 
     * @param result 执行结果
     * @return 错误信息
     */
    protected String getErrorMessage(R result) {
        return "执行失败，返回结果为空";
    }
    
    /**
     * 任务执行成功后的处理（子类可重写）
     * 
     * @param task 任务
     * @param result 执行结果
     */
    protected void onTaskSuccess(T task, R result) {
        // 默认空实现
    }
    
    /**
     * 任务执行失败后的处理（子类可重写）
     * 
     * @param task 任务
     * @param errorMessage 错误信息
     */
    protected void onTaskFailure(T task, String errorMessage) {
        // 默认空实现
    }
    
    /**
     * 任务执行异常后的处理（子类可重写）
     * 
     * @param task 任务
     * @param exception 异常信息
     */
    protected void onTaskException(T task, Exception exception) {
        // 默认空实现
    }
    
    /**
     * 批量执行结果
     */
    public static class BatchExecutionResult {
        public final int successCount;
        public final int failCount;
        
        public BatchExecutionResult(int successCount, int failCount) {
            this.successCount = successCount;
            this.failCount = failCount;
        }
        
        public int getTotalCount() {
            return successCount + failCount;
        }
        
        public double getSuccessRate() {
            int total = getTotalCount();
            return total > 0 ? (double) successCount / total : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("BatchExecutionResult{success=%d, fail=%d, total=%d, successRate=%.2f%%}", 
                    successCount, failCount, getTotalCount(), getSuccessRate() * 100);
        }
    }
}
