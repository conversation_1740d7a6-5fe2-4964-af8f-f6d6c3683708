package com.webank.maling.documentation.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * XXL-Job 配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class XxlJobConfig {
    
    @Value("${xxl.job.admin.addresses:http://localhost:8080/xxl-job-admin}")
    private String adminAddresses;
    
    @Value("${xxl.job.accessToken:}")
    private String accessToken;
    
    @Value("${xxl.job.executor.appname:maling-documentation}")
    private String appname;
    
    @Value("${xxl.job.executor.address:}")
    private String address;
    
    @Value("${xxl.job.executor.ip:}")
    private String ip;
    
    @Value("${xxl.job.executor.port:9999}")
    private int port;
    
    @Value("${xxl.job.executor.logpath:/data/applogs/xxl-job/jobhandler}")
    private String logPath;
    
    @Value("${xxl.job.executor.logretentiondays:30}")
    private int logRetentionDays;
    
    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        log.info(">>>>>>>>>>> xxl-job config init.");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(appname);
        xxlJobSpringExecutor.setAddress(address);
        xxlJobSpringExecutor.setIp(ip);
        xxlJobSpringExecutor.setPort(port);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
        
        log.info("XXL-Job 执行器配置完成 - appname: {}, port: {}", appname, port);
        return xxlJobSpringExecutor;
    }
}
