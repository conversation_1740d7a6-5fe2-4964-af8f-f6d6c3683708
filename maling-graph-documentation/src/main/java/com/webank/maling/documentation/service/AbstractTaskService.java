package com.webank.maling.documentation.service;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 抽象任务服务基类
 * 封装通用的任务管理逻辑，包括状态更新、重试处理、缓存管理等
 * 
 * @param <T> 任务实体类型
 * @param <S> 任务状态枚举类型
 * @param <M> Mapper接口类型
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractTaskService<T, S extends Enum<S>, M> {
    
    /**
     * 活跃任务缓存
     */
    protected final Map<Long, T> activeTasks = new ConcurrentHashMap<>();
    
    /**
     * 获取Mapper实例（子类实现）
     */
    protected abstract M getMapper();
    
    /**
     * 获取任务ID（子类实现）
     */
    protected abstract Long getTaskId(T task);
    
    /**
     * 获取任务状态（子类实现）
     */
    protected abstract S getTaskStatus(T task);
    
    /**
     * 设置任务状态（子类实现）
     */
    protected abstract void setTaskStatus(T task, S status);
    
    /**
     * 获取任务重试次数（子类实现）
     */
    protected abstract Integer getRetryCount(T task);
    
    /**
     * 获取最大重试次数（子类实现）
     */
    protected abstract Integer getMaxRetryCount(T task);
    
    /**
     * 设置任务更新时间（子类实现）
     */
    protected abstract void setUpdatedAt(T task, LocalDateTime time);
    
    /**
     * 设置任务完成时间（子类实现）
     */
    protected abstract void setCompletedAt(T task, LocalDateTime time);
    
    /**
     * 从数据库根据ID查询任务（子类实现）
     */
    protected abstract T findTaskById(Long taskId);
    
    /**
     * 更新任务到数据库（子类实现）
     */
    protected abstract void updateTask(T task);
    
    /**
     * 更新任务状态到数据库（子类实现）
     */
    protected abstract void updateTaskStatus(Long taskId, S status, LocalDateTime updatedAt);
    
    /**
     * 获取等待状态（子类实现）
     */
    protected abstract S getWaitingStatus();
    
    /**
     * 获取运行中状态（子类实现）
     */
    protected abstract S getRunningStatus();
    
    /**
     * 获取完成状态（子类实现）
     */
    protected abstract S getCompletedStatus();
    
    /**
     * 获取失败状态（子类实现）
     */
    protected abstract S getFailedStatus();
    
    /**
     * 获取取消状态（子类实现）
     */
    protected abstract S getCancelledStatus();
    
    /**
     * 根据ID获取任务（先从缓存，再从数据库）
     */
    public T getTaskById(Long taskId) {
        if (taskId == null) {
            return null;
        }
        
        // 先从缓存中查找
        T task = activeTasks.get(taskId);
        if (task != null) {
            return task;
        }
        
        // 从持久化存储中查找
        task = findTaskById(taskId);
        if (task != null) {
            activeTasks.put(taskId, task);
        }
        
        return task;
    }
    
    /**
     * 更新任务状态
     */
    public void updateStatus(Long taskId, S status) {
        updateStatus(taskId, status, null);
    }
    
    /**
     * 更新任务状态（带错误信息）
     */
    public void updateStatus(Long taskId, S status, String errorMessage) {
        try {
            log.debug("更新任务 {} 状态为: {}", taskId, status);
            
            LocalDateTime now = LocalDateTime.now();
            
            // 直接使用 mapper 更新，避免缓存不一致
            updateTaskStatus(taskId, status, now);
            
            // 如果有错误信息或需要设置完成时间，更新完整任务
            if (errorMessage != null || isTerminalStatus(status)) {
                T task = getTaskById(taskId);
                if (task != null) {
                    if (errorMessage != null) {
                        setTaskErrorMessage(task, errorMessage);
                    }
                    if (isTerminalStatus(status)) {
                        setCompletedAt(task, now);
                    }
                    if (status.equals(getCompletedStatus())) {
                        setTaskProgress(task, 100);
                    }
                    updateTask(task);
                }
            }
            
            // 更新缓存
            updateCachedTask(taskId, status, now, errorMessage);
            
        } catch (Exception e) {
            log.error("更新任务状态时发生错误", e);
        }
    }
    
    /**
     * 处理任务失败
     */
    public void handleTaskFailure(Long taskId, String errorMessage) {
        try {
            T task = getTaskById(taskId);
            if (task == null) {
                log.warn("任务不存在: {}", taskId);
                return;
            }
            
            Integer currentRetryCount = getRetryCount(task);
            Integer maxRetryCount = getMaxRetryCount(task);
            
            currentRetryCount = currentRetryCount != null ? currentRetryCount : 0;
            maxRetryCount = maxRetryCount != null ? maxRetryCount : 3;
            
            if (currentRetryCount < maxRetryCount) {
                // 可以重试
                int newRetryCount = currentRetryCount + 1;
                LocalDateTime nextExecuteTime = calculateNextExecuteTime(newRetryCount);
                
                updateRetryInfo(taskId, newRetryCount, nextExecuteTime, 
                               getWaitingStatus(), errorMessage);
                
                log.info("任务 {} 将进行第 {} 次重试，下次执行时间: {}", 
                        taskId, newRetryCount, nextExecuteTime);
            } else {
                // 超过最大重试次数
                updateStatus(taskId, getFailedStatus(), 
                           "超过最大重试次数: " + errorMessage);
                
                log.warn("任务 {} 超过最大重试次数，标记为最终失败", taskId);
            }
            
        } catch (Exception e) {
            log.error("处理任务失败时发生错误", e);
        }
    }
    
    /**
     * 计算下次执行时间（指数退避策略）
     */
    protected LocalDateTime calculateNextExecuteTime(int retryCount) {
        // 指数退避：1分钟、2分钟、4分钟、8分钟...
        long delayMinutes = (long) Math.pow(2, retryCount - 1);
        
        // 最大延迟不超过60分钟
        delayMinutes = Math.min(delayMinutes, 60);
        
        return LocalDateTime.now().plusMinutes(delayMinutes);
    }
    
    /**
     * 检查是否为终止状态
     */
    protected boolean isTerminalStatus(S status) {
        return status.equals(getCompletedStatus()) || 
               status.equals(getFailedStatus()) || 
               status.equals(getCancelledStatus());
    }
    
    /**
     * 更新缓存中的任务
     */
    protected void updateCachedTask(Long taskId, S status, LocalDateTime updatedAt, String errorMessage) {
        T cachedTask = activeTasks.get(taskId);
        if (cachedTask != null) {
            setTaskStatus(cachedTask, status);
            setUpdatedAt(cachedTask, updatedAt);
            if (errorMessage != null) {
                setTaskErrorMessage(cachedTask, errorMessage);
            }
            if (isTerminalStatus(status)) {
                setCompletedAt(cachedTask, updatedAt);
            }
            if (status.equals(getCompletedStatus())) {
                setTaskProgress(cachedTask, 100);
            }
        }
    }
    
    /**
     * 获取所有活跃任务
     */
    public List<T> getAllActiveTasks() {
        return List.copyOf(activeTasks.values());
    }
    
    /**
     * 清理缓存中的过期任务
     */
    public void cleanupExpiredTasksFromCache(LocalDateTime cutoffTime) {
        List<Long> expiredTaskIds = activeTasks.values().stream()
                .filter(task -> {
                    LocalDateTime completedAt = getTaskCompletedAt(task);
                    return completedAt != null && completedAt.isBefore(cutoffTime);
                })
                .map(this::getTaskId)
                .toList();
        
        for (Long taskId : expiredTaskIds) {
            activeTasks.remove(taskId);
        }
        
        log.info("从缓存中清理了 {} 个过期任务", expiredTaskIds.size());
    }
    
    // 以下方法需要子类实现具体逻辑
    
    /**
     * 设置任务错误信息（子类实现）
     */
    protected abstract void setTaskErrorMessage(T task, String errorMessage);
    
    /**
     * 设置任务进度（子类实现）
     */
    protected abstract void setTaskProgress(T task, Integer progress);
    
    /**
     * 获取任务完成时间（子类实现）
     */
    protected abstract LocalDateTime getTaskCompletedAt(T task);
    
    /**
     * 更新重试信息（子类实现）
     */
    protected abstract void updateRetryInfo(Long taskId, Integer retryCount, 
                                          LocalDateTime nextExecuteTime, S status, String errorMessage);
}
