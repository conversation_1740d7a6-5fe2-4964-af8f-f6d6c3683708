package com.webank.maling.documentation.service;

import com.webank.maling.base.entity.Documentation;
import com.webank.maling.base.entity.DocumentationTask;
import com.webank.maling.base.model.MethodInfo;
import com.webank.maling.documentation.dto.DocumentationGenerationDto;
import com.webank.maling.documentation.repository.graph.EntryPointRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 文档生成任务执行服务
 * 继承抽象基类，复用通用的任务执行逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DocumentationJobService extends AbstractJobService<DocumentationTask, DocumentationTask.TaskStatus, DocumentationGenerationDto, Documentation> {
    
    @Autowired
    private DocumentationTaskService taskService;
    
    @Autowired
    private ProgressiveDocumentationService progressiveService;
    
    @Autowired
    private EntryPointRepository entryPointRepository;
    
    /**
     * 执行文档生成任务（保持原有接口兼容性）
     *
     * @param task 要执行的任务
     * @return 是否执行成功
     */
    public boolean executeDocumentationTask(DocumentationTask task) {
        return executeTask(task);
    }
    
    /**
     * 处理任务失败，包括重试逻辑
     * 
     * @param task 失败的任务
     * @param errorMessage 错误信息
     */
    public void handleTaskFailure(DocumentationTask task, String errorMessage) {
        if (task == null) {
            return;
        }
        
        Long taskId = task.getId();
        Integer currentRetryCount = task.getRetryCount() != null ? task.getRetryCount() : 0;
        Integer maxRetryCount = task.getMaxRetryCount() != null ? task.getMaxRetryCount() : 3;
        
        log.info("处理任务失败: {}, 当前重试次数: {}, 最大重试次数: {}", 
                taskId, currentRetryCount, maxRetryCount);
        
        if (currentRetryCount < maxRetryCount) {
            // 可以重试，计算下次执行时间
            int newRetryCount = currentRetryCount + 1;
            LocalDateTime nextExecuteTime = calculateNextExecuteTime(newRetryCount);
            
            log.info("任务 {} 将进行第 {} 次重试，下次执行时间: {}", 
                    taskId, newRetryCount, nextExecuteTime);
            
            // 更新重试信息，状态改为等待
            taskService.updateRetryInfo(taskId, newRetryCount, nextExecuteTime, 
                    DocumentationTask.TaskStatus.WAITING, errorMessage);
        } else {
            // 超过最大重试次数，标记为最终失败
            log.warn("任务 {} 超过最大重试次数 {}，标记为最终失败", taskId, maxRetryCount);
            taskService.updateTaskStatus(taskId, DocumentationTask.TaskStatus.FAILED, 
                    "超过最大重试次数: " + errorMessage);
        }
    }
    
    /**
     * 计算下次执行时间
     * 使用指数退避策略
     * 
     * @param retryCount 重试次数
     * @return 下次执行时间
     */
    private LocalDateTime calculateNextExecuteTime(int retryCount) {
        // 指数退避：1分钟、2分钟、4分钟、8分钟...
        long delayMinutes = (long) Math.pow(2, retryCount - 1);
        
        // 最大延迟不超过60分钟
        delayMinutes = Math.min(delayMinutes, 60);
        
        return LocalDateTime.now().plusMinutes(delayMinutes);
    }
    
    /**
     * 检查任务是否可以执行（公开方法，供外部调用）
     *
     * @param task 任务
     * @return 是否可以执行
     */
    public boolean canExecuteTask(DocumentationTask task) {
        return isTaskExecutable(task);
    }

    // ==================== 实现抽象基类的方法 ====================

    @Override
    protected AbstractTaskService<DocumentationTask, DocumentationTask.TaskStatus, ?> getTaskService() {
        return taskService;
    }

    @Override
    protected Long getTaskId(DocumentationTask task) {
        return task != null ? task.getId() : null;
    }

    @Override
    protected String getTaskDescription(DocumentationTask task) {
        if (task == null) {
            return "unknown";
        }
        return String.format("入口点: %s, 目标层级: %d", task.getEntryPointId(), task.getTargetLevel());
    }

    @Override
    protected boolean canExecuteTask(DocumentationTask task) {
        // 调用本类的 canExecuteTask 方法，但需要重命名以避免递归
        return isTaskExecutable(task);
    }

    /**
     * 检查任务是否可以执行（重命名方法以避免与基类方法冲突）
     */
    private boolean isTaskExecutable(DocumentationTask task) {
        if (task == null) {
            return false;
        }

        // 检查状态
        if (task.getStatus() != DocumentationTask.TaskStatus.WAITING) {
            return false;
        }

        // 检查重试次数
        Integer retryCount = task.getRetryCount() != null ? task.getRetryCount() : 0;
        Integer maxRetryCount = task.getMaxRetryCount() != null ? task.getMaxRetryCount() : 3;

        if (retryCount >= maxRetryCount) {
            return false;
        }

        // 检查执行时间
        LocalDateTime nextExecuteTime = task.getNextExecuteTime();
        if (nextExecuteTime != null && LocalDateTime.now().isBefore(nextExecuteTime)) {
            return false;
        }

        return true;
    }

    @Override
    protected Documentation doExecuteTask(DocumentationTask task, DocumentationGenerationDto dto) throws Exception {
        String entryPointId = task.getEntryPointId();

        // 检查入口点是否存在
        MethodInfo entryPoint = entryPointRepository.getEntryPointById(entryPointId);
        if (entryPoint == null) {
            throw new IllegalArgumentException("入口点不存在: " + entryPointId);
        }

        // 执行渐进式生成
        return progressiveService.executeProgressiveGeneration(task, dto, entryPoint);
    }

    @Override
    protected DocumentationGenerationDto buildTaskRequest(DocumentationTask task) {
        return DocumentationGenerationDto.builder()
                .projectId(task.getProjectId())
                .branchName(task.getBranchName())
                .level(task.getTargetLevel())
                .forceRegenerate(true) // 任务执行时强制重新生成
                .description("定时任务执行")
                .build();
    }

    @Override
    protected DocumentationTask.TaskStatus getRunningStatus() {
        return DocumentationTask.TaskStatus.RUNNING;
    }

    @Override
    protected DocumentationTask.TaskStatus getCompletedStatus() {
        return DocumentationTask.TaskStatus.COMPLETED;
    }

    @Override
    protected DocumentationTask.TaskStatus getFailedStatus() {
        return DocumentationTask.TaskStatus.FAILED;
    }

    @Override
    protected void onTaskSuccess(DocumentationTask task, Documentation result) {
        log.info("任务 {} 执行成功，生成文档ID: {}", task.getId(), result.getId());
    }

    @Override
    protected void onTaskFailure(DocumentationTask task, String errorMessage) {
        log.error("任务 {} 执行失败: {}", task.getId(), errorMessage);
    }

    @Override
    protected void onTaskException(DocumentationTask task, Exception exception) {
        log.error("任务 {} 执行异常", task.getId(), exception);
    }
}
