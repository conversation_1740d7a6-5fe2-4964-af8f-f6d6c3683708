package com.webank.maling.documentation.job;

import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 抽象任务处理器基类
 * 封装通用的任务处理逻辑，包括参数解析、批处理、异常处理等
 * 
 * @param <T> 任务实体类型
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractTaskJobHandler<T> {
    
    /**
     * 执行批处理任务的通用模板方法
     * 
     * @param jobName 任务名称
     * @param defaultBatchSize 默认批处理大小
     * @param taskSupplier 获取待处理任务的函数
     * @param taskProcessor 处理单个任务的函数
     */
    protected void executeBatchJob(String jobName, 
                                 int defaultBatchSize,
                                 Function<Integer, List<T>> taskSupplier,
                                 Function<T, Boolean> taskProcessor) {
        String param = XxlJobHelper.getJobParam();
        log.info("开始执行{}，参数: {}", jobName, param);
        
        try {
            // 解析批处理大小
            int batchSize = parseBatchSize(param, defaultBatchSize);
            
            // 获取待处理任务
            List<T> tasks = taskSupplier.apply(batchSize);
            
            if (tasks.isEmpty()) {
                log.debug("没有待处理的任务");
                return;
            }
            
            log.info("找到 {} 个待处理任务", tasks.size());
            
            // 批处理任务
            BatchResult result = processBatch(tasks, taskProcessor);
            
            log.info("{}完成 - 成功: {}, 失败: {}", jobName, result.successCount, result.failCount);
            
        } catch (Exception e) {
            log.error("执行{}时发生异常", jobName, e);
            XxlJobHelper.handleFail("执行" + jobName + "失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行简单任务的通用模板方法
     * 
     * @param jobName 任务名称
     * @param defaultParam 默认参数
     * @param taskExecutor 任务执行器
     */
    protected void executeSimpleJob(String jobName, 
                                  String defaultParam,
                                  Function<String, Integer> taskExecutor) {
        String param = XxlJobHelper.getJobParam();
        log.info("开始执行{}，参数: {}", jobName, param);
        
        try {
            // 使用参数或默认值
            String actualParam = StringUtils.hasText(param) ? param.trim() : defaultParam;
            
            // 执行任务
            Integer result = taskExecutor.apply(actualParam);
            
            log.info("{}完成，处理数量: {}", jobName, result);
            
        } catch (Exception e) {
            log.error("执行{}时发生异常", jobName, e);
            XxlJobHelper.handleFail("执行" + jobName + "失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析批处理大小参数
     * 
     * @param param 参数字符串
     * @param defaultSize 默认大小
     * @return 批处理大小
     */
    protected int parseBatchSize(String param, int defaultSize) {
        if (!StringUtils.hasText(param)) {
            return defaultSize;
        }
        
        try {
            int size = Integer.parseInt(param.trim());
            return Math.max(1, Math.min(size, 100)); // 限制在1-100之间
        } catch (NumberFormatException e) {
            log.warn("任务参数格式错误，使用默认批处理大小: {}", defaultSize);
            return defaultSize;
        }
    }
    
    /**
     * 解析整数参数
     * 
     * @param param 参数字符串
     * @param defaultValue 默认值
     * @return 解析后的整数
     */
    protected int parseIntParam(String param, int defaultValue) {
        if (!StringUtils.hasText(param)) {
            return defaultValue;
        }
        
        try {
            return Integer.parseInt(param.trim());
        } catch (NumberFormatException e) {
            log.warn("参数格式错误，使用默认值: {}", defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 批处理任务
     * 
     * @param tasks 任务列表
     * @param processor 任务处理器
     * @return 批处理结果
     */
    protected BatchResult processBatch(List<T> tasks, Function<T, Boolean> processor) {
        int successCount = 0;
        int failCount = 0;
        
        for (T task : tasks) {
            try {
                String taskId = getTaskId(task);
                log.info("开始处理任务: {}", taskId);
                
                boolean success = processor.apply(task);
                
                if (success) {
                    successCount++;
                    log.info("任务 {} 处理成功", taskId);
                } else {
                    failCount++;
                    log.warn("任务 {} 处理失败", taskId);
                }
                
            } catch (Exception e) {
                failCount++;
                String taskId = getTaskId(task);
                log.error("处理任务 {} 时发生异常", taskId, e);
                
                // 处理任务异常
                handleTaskException(task, e);
            }
        }
        
        return new BatchResult(successCount, failCount);
    }
    
    /**
     * 安全执行操作，捕获异常并记录日志
     * 
     * @param operation 操作描述
     * @param action 要执行的操作
     */
    protected void safeExecute(String operation, Runnable action) {
        try {
            action.run();
        } catch (Exception e) {
            log.error("执行{}时发生异常", operation, e);
        }
    }
    
    /**
     * 安全执行操作，捕获异常并记录日志，返回结果
     * 
     * @param operation 操作描述
     * @param action 要执行的操作
     * @param defaultValue 默认返回值
     * @return 操作结果或默认值
     */
    protected <R> R safeExecute(String operation, Supplier<R> action, R defaultValue) {
        try {
            return action.get();
        } catch (Exception e) {
            log.error("执行{}时发生异常", operation, e);
            return defaultValue;
        }
    }
    
    /**
     * 获取任务ID（子类需要实现）
     * 
     * @param task 任务对象
     * @return 任务ID字符串
     */
    protected abstract String getTaskId(T task);
    
    /**
     * 处理任务异常（子类可以重写）
     * 
     * @param task 出现异常的任务
     * @param exception 异常信息
     */
    protected void handleTaskException(T task, Exception exception) {
        // 默认实现：记录日志
        log.error("任务处理异常，任务ID: {}", getTaskId(task), exception);
    }
    
    /**
     * 批处理结果
     */
    protected static class BatchResult {
        public final int successCount;
        public final int failCount;
        
        public BatchResult(int successCount, int failCount) {
            this.successCount = successCount;
            this.failCount = failCount;
        }
    }
}
