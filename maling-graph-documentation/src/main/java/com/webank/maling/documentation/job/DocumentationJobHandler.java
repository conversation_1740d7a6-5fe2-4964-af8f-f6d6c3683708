package com.webank.maling.documentation.job;

import com.webank.maling.base.entity.DocumentationTask;
import com.webank.maling.documentation.service.DocumentationTaskService;
import com.webank.maling.documentation.service.DocumentationJobService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 文档生成任务处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DocumentationJobHandler {
    
    @Autowired
    private DocumentationTaskService taskService;
    
    @Autowired
    private DocumentationJobService jobService;
    
    /**
     * 文档生成任务处理
     * 每分钟执行一次，处理等待中的任务
     */
    @XxlJob("documentationGenerationJob")
    public void documentationGenerationJob() {
        String param = XxlJobHelper.getJobParam();
        log.info("开始执行文档生成任务，参数: {}", param);
        
        try {
            // 获取批处理大小，默认为5
            int batchSize = 5;
            if (param != null && !param.trim().isEmpty()) {
                try {
                    batchSize = Integer.parseInt(param.trim());
                } catch (NumberFormatException e) {
                    log.warn("任务参数格式错误，使用默认批处理大小: {}", batchSize);
                }
            }
            
            // 获取等待执行的任务
            List<DocumentationTask> waitingTasks = taskService.getWaitingTasks(batchSize);
            
            if (waitingTasks.isEmpty()) {
                log.debug("没有等待执行的任务");
                return;
            }
            
            log.info("找到 {} 个等待执行的任务", waitingTasks.size());
            
            // 处理每个任务
            int successCount = 0;
            int failCount = 0;
            
            for (DocumentationTask task : waitingTasks) {
                try {
                    log.info("开始处理任务: {}, 入口点: {}", task.getId(), task.getEntryPointId());
                    
                    // 执行任务
                    boolean success = jobService.executeDocumentationTask(task);
                    
                    if (success) {
                        successCount++;
                        log.info("任务 {} 执行成功", task.getId());
                    } else {
                        failCount++;
                        log.warn("任务 {} 执行失败", task.getId());
                    }
                    
                } catch (Exception e) {
                    failCount++;
                    log.error("处理任务 {} 时发生异常", task.getId(), e);
                    
                    // 更新任务失败状态
                    try {
                        taskService.handleTaskFailure(task.getId(), e.getMessage());
                    } catch (Exception ex) {
                        log.error("更新任务失败状态时发生异常", ex);
                    }
                }
            }
            
            log.info("批处理完成 - 成功: {}, 失败: {}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("执行文档生成任务时发生异常", e);
            XxlJobHelper.handleFail("执行文档生成任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 任务清理作业
     * 每天凌晨2点执行，清理过期任务
     */
    @XxlJob("documentationCleanupJob")
    public void documentationCleanupJob() {
        String param = XxlJobHelper.getJobParam();
        log.info("开始执行任务清理作业，参数: {}", param);
        
        try {
            // 获取保留天数，默认为30天
            int retentionDays = 30;
            if (param != null && !param.trim().isEmpty()) {
                try {
                    retentionDays = Integer.parseInt(param.trim());
                } catch (NumberFormatException e) {
                    log.warn("任务参数格式错误，使用默认保留天数: {}", retentionDays);
                }
            }
            
            // 执行清理
            int cleanedCount = taskService.cleanupExpiredTasks(retentionDays);
            
            log.info("任务清理完成，清理了 {} 个过期任务", cleanedCount);
            
        } catch (Exception e) {
            log.error("执行任务清理作业时发生异常", e);
            XxlJobHelper.handleFail("执行任务清理失败: " + e.getMessage());
        }
    }
    
    /**
     * 失败任务重试作业
     * 每小时执行一次，处理可重试的失败任务
     */
    @XxlJob("documentationRetryJob")
    public void documentationRetryJob() {
        String param = XxlJobHelper.getJobParam();
        log.info("开始执行失败任务重试作业，参数: {}", param);
        
        try {
            // 获取批处理大小，默认为10
            int batchSize = 10;
            if (param != null && !param.trim().isEmpty()) {
                try {
                    batchSize = Integer.parseInt(param.trim());
                } catch (NumberFormatException e) {
                    log.warn("任务参数格式错误，使用默认批处理大小: {}", batchSize);
                }
            }
            
            // 处理可重试的失败任务
            int retryCount = taskService.retryFailedTasks(batchSize);
            
            log.info("失败任务重试完成，重试了 {} 个任务", retryCount);
            
        } catch (Exception e) {
            log.error("执行失败任务重试作业时发生异常", e);
            XxlJobHelper.handleFail("执行失败任务重试失败: " + e.getMessage());
        }
    }
}
